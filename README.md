# Sistema de Busca de Produtos - Carrefour

Este é um sistema web que permite buscar produtos no site do Carrefour e visualizar suas imagens de forma organizada.

## 🚀 Funcionalidades

- ✅ Interface web moderna e responsiva
- ✅ Busca de produtos por nome
- ✅ Extração automática de imagens dos produtos
- ✅ Exibição organizada em cards
- ✅ Tratamento de erros e loading
- ✅ Design mobile-friendly

## 📋 Pré-requisitos

- Python 3.7 ou superior
- pip (gerenciador de pacotes do Python)

## 🔧 Instalação

1. **Clone ou baixe este projeto**

2. **Instale as dependências:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Execute a aplicação:**
   ```bash
   python app.py
   ```

4. **Acesse no navegador:**
   ```
   http://localhost:5000
   ```

## 📱 Como Usar

1. **Abra o navegador** e acesse `http://localhost:5000`

2. **Digite o nome do produto** que deseja buscar (ex: "Gin", "<PERSON><PERSON><PERSON>", "Chocolate")

3. **Clique em "Buscar"** ou pressione Enter

4. **Visualize os resultados** com as imagens dos produtos encontrados

## 🛠️ Estrutura do Projeto

```
apifoto1/
├── app.py              # Aplicação Flask principal
├── requirements.txt    # Dependências do projeto
├── README.md          # Este arquivo
└── templates/
    └── index.html     # Interface web
```

## ⚠️ Avisos Importantes

### Legalidade
- Este sistema faz scraping do site do Carrefour
- Verifique os termos de serviço antes de usar em produção
- Use com responsabilidade e moderação
- Considere APIs oficiais quando disponíveis

### Limitações Técnicas
- O site pode mudar sua estrutura, afetando o funcionamento
- Alguns produtos podem não aparecer se carregados via JavaScript
- Rate limiting pode bloquear muitas requisições seguidas

## 🔍 Como Funciona

1. **Frontend (HTML/CSS/JS):**
   - Interface moderna com design responsivo
   - Formulário de busca com validação
   - Exibição dos resultados em grid de cards
   - Loading e tratamento de erros

2. **Backend (Flask/Python):**
   - Recebe o nome do produto via API
   - Faz requisição para o site do Carrefour
   - Extrai informações dos produtos usando BeautifulSoup
   - Retorna dados estruturados em JSON

3. **Scraping:**
   - Usa múltiplos padrões para encontrar produtos
   - Extrai imagens, nomes e preços
   - Filtra resultados relevantes
   - Trata diferentes estruturas HTML

## 🐛 Solução de Problemas

### Erro "Nenhum produto encontrado"
- Verifique a ortografia do produto
- Tente termos mais genéricos (ex: "gin" ao invés de "gin tanqueray")
- O site pode estar temporariamente indisponível

### Erro de conexão
- Verifique sua conexão com a internet
- O site do Carrefour pode estar bloqueando requisições
- Tente aguardar alguns minutos e tentar novamente

### Imagens não carregam
- Algumas URLs de imagem podem estar quebradas
- O site pode ter mudado a estrutura das imagens
- Imagens podem ter proteção contra hotlinking

## 🔄 Melhorias Futuras

- [ ] Implementar cache para evitar requisições repetidas
- [ ] Adicionar filtros por preço e categoria
- [ ] Implementar paginação para mais resultados
- [ ] Adicionar opção de salvar produtos favoritos
- [ ] Implementar busca em outros sites
- [ ] Adicionar API para integração externa

## 📞 Suporte

Se encontrar problemas ou tiver sugestões:
1. Verifique se todas as dependências estão instaladas
2. Confirme que está usando Python 3.7+
3. Teste com diferentes termos de busca
4. Verifique os logs no terminal para erros específicos

## 📄 Licença

Este projeto é para fins educacionais e de demonstração. Use com responsabilidade e respeite os termos de serviço dos sites utilizados.
