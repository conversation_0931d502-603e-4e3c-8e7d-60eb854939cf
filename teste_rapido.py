import requests
from bs4 import BeautifulSoup
from urllib.parse import quote
import re

def teste_carrefour_rapido(produto):
    print(f"🔍 Testando busca rápida por: {produto}")
    
    urls_para_testar = [
        f"https://www.carrefour.com.br/{quote(produto)}",
        f"https://www.carrefour.com.br/busca/{quote(produto)}",
        f"https://www.carrefour.com.br/search?q={quote(produto)}",
        f"https://www.carrefour.com.br/produtos/{quote(produto)}",
    ]
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
    }
    
    for url in urls_para_testar:
        try:
            print(f"\n📡 Testando: {url}")
            response = requests.get(url, headers=headers, timeout=8)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Conta imagens
                imagens = soup.find_all('img')
                print(f"📷 Imagens encontradas: {len(imagens)}")
                
                # Procura por produtos
                produtos_encontrados = 0
                for img in imagens[:10]:  # Testa só as primeiras 10
                    src = img.get('src', '')
                    alt = img.get('alt', '')
                    
                    if src and ('product' in src.lower() or produto.lower() in alt.lower()):
                        produtos_encontrados += 1
                        print(f"✅ Produto: {alt[:50]}... - {src[:50]}...")
                
                print(f"🎯 Produtos relevantes: {produtos_encontrados}")
                
                if produtos_encontrados > 0:
                    print(f"🎉 SUCESSO! URL funcionou: {url}")
                    return True
                    
            else:
                print(f"❌ Erro {response.status_code}")
                
        except Exception as e:
            print(f"❌ Erro: {e}")
    
    print("😞 Nenhuma URL funcionou")
    return False

if __name__ == "__main__":
    # Testa com diferentes produtos
    produtos = ["coca", "gin", "cerveja"]
    
    for produto in produtos:
        print(f"\n{'='*50}")
        teste_carrefour_rapido(produto)
        print(f"{'='*50}")
