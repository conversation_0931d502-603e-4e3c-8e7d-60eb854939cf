<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Busca de Produtos - Carrefour</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #0066cc, #004499);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .search-section {
            padding: 40px;
            background: #f8f9fa;
        }

        .search-form {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .search-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 50px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: #0066cc;
            box-shadow: 0 0 0 3px rgba(0,102,204,0.1);
        }

        .search-btn {
            padding: 15px 30px;
            background: linear-gradient(135deg, #0066cc, #004499);
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,102,204,0.3);
        }

        .search-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            text-align: center;
            padding: 40px;
            display: none;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0066cc;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results {
            padding: 0 40px 40px;
        }

        .results-header {
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e0e0e0;
        }

        .results-count {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 10px;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .product-info {
            padding: 20px;
        }

        .product-name {
            font-size: 1.1em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .product-price {
            font-size: 1.2em;
            font-weight: 700;
            color: #0066cc;
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #c33;
        }

        .no-results {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .no-results h3 {
            font-size: 1.5em;
            margin-bottom: 15px;
        }

        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .search-section {
                padding: 20px;
            }
            
            .results {
                padding: 0 20px 20px;
            }
            
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛒 Busca de Produtos</h1>
            <p>Encontre produtos do Carrefour e visualize suas imagens</p>
        </div>

        <div class="search-section">
            <form class="search-form" id="searchForm">
                <input 
                    type="text" 
                    class="search-input" 
                    id="produtoInput" 
                    placeholder="Digite o nome do produto (ex: Gin, Cerveja, Chocolate...)"
                    required
                    minlength="2"
                >
                <button type="submit" class="search-btn" id="searchBtn">
                    🔍 Buscar
                </button>
            </form>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Buscando produtos...</p>
        </div>

        <div class="results" id="results" style="display: none;">
            <div class="results-header">
                <div class="results-count" id="resultsCount"></div>
            </div>
            <div class="products-grid" id="productsGrid"></div>
        </div>
    </div>

    <script>
        document.getElementById('searchForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const produto = document.getElementById('produtoInput').value.trim();
            const searchBtn = document.getElementById('searchBtn');
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            
            if (!produto || produto.length < 2) {
                alert('Por favor, digite pelo menos 2 caracteres para o produto.');
                return;
            }
            
            // Mostra loading
            searchBtn.disabled = true;
            searchBtn.textContent = 'Buscando...';
            loading.style.display = 'block';
            results.style.display = 'none';
            
            try {
                const response = await fetch('/buscar', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ produto: produto })
                });
                
                const data = await response.json();
                
                // Esconde loading
                loading.style.display = 'none';
                searchBtn.disabled = false;
                searchBtn.textContent = '🔍 Buscar';
                
                if (data.erro) {
                    showError(data.erro);
                } else {
                    showResults(data);
                }
                
            } catch (error) {
                loading.style.display = 'none';
                searchBtn.disabled = false;
                searchBtn.textContent = '🔍 Buscar';
                showError('Erro de conexão. Tente novamente.');
            }
        });
        
        function showError(message) {
            const results = document.getElementById('results');
            results.innerHTML = `<div class="error">❌ ${message}</div>`;
            results.style.display = 'block';
        }
        
        function showResults(data) {
            const results = document.getElementById('results');
            const resultsCount = document.getElementById('resultsCount');
            const productsGrid = document.getElementById('productsGrid');
            
            if (!data.produtos || data.produtos.length === 0) {
                results.innerHTML = `
                    <div class="no-results">
                        <h3>😔 Nenhum produto encontrado</h3>
                        <p>Tente buscar com outros termos ou verifique a ortografia.</p>
                    </div>
                `;
                results.style.display = 'block';
                return;
            }
            
            resultsCount.textContent = `${data.produtos.length} produto(s) encontrado(s)`;
            
            productsGrid.innerHTML = data.produtos.map(produto => `
                <div class="product-card">
                    <img 
                        src="${produto.imagem}" 
                        alt="${produto.nome}"
                        class="product-image"
                        onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlbSBuw6NvIGRpc3BvbsOtdmVsPC90ZXh0Pjwvc3ZnPg=='"
                    >
                    <div class="product-info">
                        <div class="product-name">${produto.nome}</div>
                        <div class="product-price">${produto.preco}</div>
                    </div>
                </div>
            `).join('');
            
            results.style.display = 'block';
        }
    </script>
</body>
</html>
