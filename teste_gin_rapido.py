import requests
from bs4 import BeautifulSoup

def teste_gin_simples():
    print("🧪 Testando gin com filtro simplificado...")
    
    url = "https://www.carrefour.com.br/busca/gin"
    headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}
    
    response = requests.get(url, headers=headers, timeout=10)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        soup = BeautifulSoup(response.text, 'html.parser')
        imagens = soup.find_all('img')
        print(f"Total imagens: {len(imagens)}")
        
        produtos = 0
        exclusoes = ['mastercard', 'visa', 'elo', 'diners', 'american express', 
                    'boleto', 'pix', 'itau', 'bradesco', 'santander', 'nubank']
        
        for img in imagens:
            src = img.get('src', '')
            alt = img.get('alt', '')
            
            # Pula ícones de pagamento
            if any(excl in alt.lower() for excl in exclusoes):
                continue
                
            # Se é do Carrefour, é produto
            if 'carrefourbr.vtexassets.com' in src.lower():
                produtos += 1
                print(f"✅ {alt[:60]}...")
                
                if produtos >= 5:
                    break
        
        print(f"\n🎯 Produtos encontrados: {produtos}")
        return produtos > 0
    
    return False

if __name__ == "__main__":
    teste_gin_simples()
