from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
import time

def testar_busca_carrefour():
    driver = None
    try:
        print("Testando busca no Carrefour...")
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        
        # Acessa página principal
        print("Acessando Carrefour...")
        driver.get("https://www.carrefour.com.br")
        time.sleep(5)
        
        # Aceita cookies
        try:
            cookie_btn = driver.find_element(By.XPATH, "//button[contains(text(), 'Aceitar')]")
            cookie_btn.click()
            print("Cookies aceitos")
            time.sleep(2)
        except:
            print("Sem cookies para aceitar")
        
        # Procura caixa de busca
        search_selectors = [
            "input[placeholder*='busca']",
            "input[placeholder*='Busca']", 
            "input[type='search']",
            "input[name*='search']"
        ]
        
        search_box = None
        for selector in search_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        search_box = element
                        print(f"Caixa de busca encontrada: {selector}")
                        break
                if search_box:
                    break
            except:
                continue
        
        if search_box:
            # Faz a busca
            search_box.clear()
            search_box.send_keys("coca cola")
            search_box.send_keys(Keys.RETURN)
            print("Busca enviada...")
            time.sleep(8)
            
            # Verifica produtos
            produtos = driver.find_elements(By.CSS_SELECTOR, "img[src*='product'], img[alt*='coca'], div[class*='product']")
            print(f"Produtos encontrados: {len(produtos)}")
            
            # Mostra alguns produtos
            for i, produto in enumerate(produtos[:5]):
                try:
                    if produto.tag_name == 'img':
                        alt = produto.get_attribute('alt')
                        src = produto.get_attribute('src')
                        print(f"Produto {i+1}: {alt} - {src[:50]}...")
                except:
                    pass
                    
        else:
            print("Caixa de busca não encontrada")
            # Mostra produtos da página principal
            produtos = driver.find_elements(By.CSS_SELECTOR, "img")
            print(f"Imagens na página: {len(produtos)}")
            
        return True
        
    except Exception as e:
        print(f"Erro: {e}")
        return False
        
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    testar_busca_carrefour()
