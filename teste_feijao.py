import requests
from bs4 import BeautifulSoup

def teste_feijao():
    print("🧪 Testando 'feijão'...")
    
    # Remove espaços e acentos
    termo = "feijão".replace(' ', '').replace('ã', 'a').replace('ç', 'c')
    url = f"https://www.carrefour.com.br/busca/{termo}"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    
    print(f"📡 URL: {url}")
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            imagens = soup.find_all('img')
            
            print(f"📷 Total de imagens: {len(imagens)}")
            
            # Exclusões
            exclusoes = [
                'mastercard', 'visa', 'elo', 'diners', 'american express', 
                'boleto', 'pix', 'itau', 'bradesco', 'santander', 'nubank',
                'original', 'vale troca', 'cupom', 'carrefour', 'logo'
            ]
            
            produtos_feijao = 0
            for img in imagens:
                alt = img.get('alt', '').lower()
                src = img.get('src', '').lower()
                
                # Pula se for ícone de pagamento
                if any(excl in alt or excl in src for excl in exclusoes):
                    continue
                
                # Verifica se é produto do Carrefour e contém "feijao"
                if 'carrefourbr.vtexassets.com' in src and 'feija' in alt:
                    produtos_feijao += 1
                    print(f"✅ {img.get('alt', 'Sem nome')[:60]}...")
                    
                    if produtos_feijao >= 10:  # Mostra só os primeiros 10
                        break
            
            print(f"\n🎯 Produtos de feijão encontrados: {produtos_feijao}")
            
            if produtos_feijao > 0:
                print("🎉 SUCESSO! Feijão encontrado!")
                return True
            else:
                print("😞 Nenhum produto de feijão encontrado")
                
                # Debug: mostra algumas imagens para entender o problema
                print("\n🔍 Primeiras 10 imagens para debug:")
                for i, img in enumerate(imagens[:10]):
                    alt = img.get('alt', 'Sem alt')
                    src = img.get('src', 'Sem src')
                    print(f"{i+1}. ALT: {alt[:40]} | SRC: {src[:50]}...")
                
                return False
        else:
            print(f"❌ Erro {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    teste_feijao()
