from flask import Flask, render_template, request, jsonify
import requests
from bs4 import BeautifulSoup
import time
import re
from urllib.parse import urljoin, quote
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import os

app = Flask(__name__)

def extrair_imagens_mercadolivre(nome_produto):
    """
    Extrai URLs das imagens de produtos do Mercado Livre usando Selenium
    """
    driver = None
    try:
        print(f"Buscando no Mercado Livre: {nome_produto}")

        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)

        # URL de busca do Mercado Livre
        url = f"https://lista.mercadolivre.com.br/{quote(nome_produto)}"
        print(f"Acessando: {url}")

        driver.get(url)
        time.sleep(5)

        produtos = []

        # Busca produtos no Mercado Livre
        elementos_produtos = driver.find_elements(By.CSS_SELECTOR, ".ui-search-result")

        print(f"Produtos encontrados no ML: {len(elementos_produtos)}")

        for i, elemento in enumerate(elementos_produtos[:15]):
            try:
                # Imagem
                img = elemento.find_element(By.CSS_SELECTOR, "img")
                img_url = img.get_attribute('src') or img.get_attribute('data-src')

                # Nome
                nome_elem = elemento.find_element(By.CSS_SELECTOR, ".ui-search-item__title")
                nome = nome_elem.text.strip()

                # Preço
                try:
                    preco_elem = elemento.find_element(By.CSS_SELECTOR, ".andes-money-amount__fraction")
                    preco = f"R$ {preco_elem.text}"
                except:
                    preco = "Consulte"

                if img_url and nome:
                    produtos.append({
                        'nome': nome[:100],
                        'imagem': img_url,
                        'preco': preco
                    })
                    print(f"ML Produto {i+1}: {nome[:50]}...")

            except Exception as e:
                print(f"Erro produto ML {i+1}: {e}")
                continue

        if produtos:
            return {
                "produtos": produtos,
                "total": len(produtos),
                "url_busca": url,
                "debug_info": f"Mercado Livre - {len(produtos)} produtos encontrados"
            }
        else:
            return {"erro": "Nenhum produto encontrado no Mercado Livre"}

    except Exception as e:
        print(f"Erro no Mercado Livre: {e}")
        return {"erro": f"Erro ao acessar Mercado Livre: {str(e)}"}

    finally:
        if driver:
            driver.quit()

def extrair_imagens_carrefour_selenium(nome_produto):
    """
    Extrai URLs das imagens de produtos do Carrefour usando Selenium
    """
    driver = None
    try:
        print(f"Iniciando busca por: {nome_produto}")

        # Configurações do Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Executa sem interface gráfica
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

        # Inicializa o driver
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)

        # Acessa a página principal primeiro
        url = "https://www.carrefour.com.br"
        print(f"Acessando página principal: {url}")

        driver.get(url)

        # Aguarda a página carregar
        time.sleep(5)

        # Tenta aceitar cookies se aparecer
        try:
            cookie_button = WebDriverWait(driver, 5).until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Aceitar') or contains(text(), 'Accept') or contains(@id, 'accept') or contains(@class, 'accept')]"))
            )
            cookie_button.click()
            print("Cookies aceitos")
            time.sleep(2)
        except TimeoutException:
            print("Botão de cookies não encontrado ou não necessário")

        # Procura pela caixa de busca e faz a pesquisa
        try:
            print(f"Procurando caixa de busca para: {nome_produto}")

            # Diferentes seletores para a caixa de busca
            search_selectors = [
                "input[placeholder*='busca']",
                "input[placeholder*='Busca']",
                "input[placeholder*='produto']",
                "input[type='search']",
                "input[name*='search']",
                "input[id*='search']",
                "#search",
                ".search-input"
            ]

            search_box = None
            for selector in search_selectors:
                try:
                    search_box = driver.find_element(By.CSS_SELECTOR, selector)
                    if search_box.is_displayed():
                        print(f"Caixa de busca encontrada com: {selector}")
                        break
                except:
                    continue

            if search_box:
                # Limpa e digita o termo de busca
                search_box.clear()
                search_box.send_keys(nome_produto)
                time.sleep(1)

                # Tenta enviar o formulário
                try:
                    search_box.submit()
                except:
                    # Se submit não funcionar, tenta pressionar Enter
                    from selenium.webdriver.common.keys import Keys
                    search_box.send_keys(Keys.RETURN)

                print("Busca enviada, aguardando resultados...")
                time.sleep(8)  # Aguarda mais tempo para carregar resultados

            else:
                print("Caixa de busca não encontrada, usando produtos da página principal")

        except Exception as e:
            print(f"Erro na busca: {e}, usando produtos da página principal")

        produtos = []

        # Busca por produtos usando diferentes seletores
        seletores_produto = [
            "div[data-testid*='product']",
            "div[class*='product-summary']",
            "div[class*='vtex-product-summary']",
            "article[class*='product']",
            ".shelf-item",
            "[data-product-id]",
            "div[class*='product-card']",
            "div[class*='item-card']",
            "a[class*='product']",
            ".product-item"
        ]

        elementos_produtos = []
        for seletor in seletores_produto:
            try:
                elementos = driver.find_elements(By.CSS_SELECTOR, seletor)
                if elementos:
                    print(f"Encontrados {len(elementos)} produtos com seletor: {seletor}")
                    elementos_produtos = elementos
                    break
            except Exception as e:
                print(f"Erro com seletor {seletor}: {e}")
                continue

        if not elementos_produtos:
            # Busca genérica por imagens de produtos
            print("Tentando busca genérica por imagens...")
            elementos_produtos = driver.find_elements(By.CSS_SELECTOR, "img[src*='carrefour'], img[alt*='produto'], img[class*='product']")

        print(f"Total de elementos encontrados: {len(elementos_produtos)}")

        for i, elemento in enumerate(elementos_produtos[:20]):  # Limita a 20 produtos
            try:
                produto_info = extrair_info_produto_selenium(elemento, driver)
                if produto_info:
                    produtos.append(produto_info)
                    print(f"Produto {i+1}: {produto_info['nome'][:50]}...")
            except Exception as e:
                print(f"Erro ao extrair produto {i+1}: {e}")
                continue

        if not produtos:
            return {"erro": "Nenhum produto encontrado na página"}

        return {
            "produtos": produtos,
            "total": len(produtos),
            "url_busca": url,
            "debug_info": f"Selenium - {len(produtos)} produtos encontrados"
        }

    except Exception as e:
        print(f"Erro no Selenium: {e}")
        return {"erro": f"Erro ao usar Selenium: {str(e)}"}

    finally:
        if driver:
            driver.quit()

def extrair_info_produto_selenium(elemento, driver):
    """
    Extrai informações de um elemento de produto usando Selenium
    """
    try:
        # Busca pela imagem
        img = None
        img_selectors = [
            ".//img",
            ".//img[contains(@class, 'product')]",
            ".//img[contains(@alt, 'produto')]"
        ]

        for selector in img_selectors:
            try:
                img = elemento.find_element(By.XPATH, selector)
                if img:
                    break
            except:
                continue

        if not img:
            return None

        img_url = img.get_attribute('src') or img.get_attribute('data-src')
        if not img_url or 'data:image' in img_url:
            return None

        # Busca pelo nome do produto
        nome = ""
        nome_selectors = [
            ".//h2",
            ".//h3",
            ".//span[contains(@class, 'name')]",
            ".//a[contains(@class, 'name')]",
            ".//div[contains(@class, 'name')]"
        ]

        for selector in nome_selectors:
            try:
                nome_element = elemento.find_element(By.XPATH, selector)
                nome = nome_element.text.strip()
                if nome:
                    break
            except:
                continue

        if not nome:
            nome = img.get_attribute('alt') or "Produto"

        # Busca pelo preço
        preco = "N/A"
        preco_selectors = [
            ".//span[contains(@class, 'price')]",
            ".//div[contains(@class, 'price')]",
            ".//span[contains(text(), 'R$')]",
            ".//*[contains(text(), 'R$')]"
        ]

        for selector in preco_selectors:
            try:
                preco_element = elemento.find_element(By.XPATH, selector)
                preco_text = preco_element.text.strip()
                if 'R$' in preco_text:
                    preco = preco_text
                    break
            except:
                continue

        return {
            'nome': nome[:100],  # Limita o tamanho
            'imagem': img_url,
            'preco': preco
        }

    except Exception as e:
        print(f"Erro ao extrair info do produto: {e}")
        return None

def extrair_imagens_carrefour_rapido(nome_produto):
    """
    Versão rápida e simples para extrair produtos do Carrefour
    """
    try:
        print(f"🔍 Buscando '{nome_produto}' no Carrefour...")

        # Remove espaços e caracteres especiais
        termo_limpo = nome_produto.replace(' ', '').replace('ã', 'a').replace('ç', 'c').replace('õ', 'o')
        url = f"https://www.carrefour.com.br/busca/{termo_limpo}"

        # Headers mais simples mas eficazes
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "pt-BR,pt;q=0.9",
            "Connection": "keep-alive"
        }

        print(f"📡 Acessando: {url}")
        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code == 200:
            print("✅ Página carregada com sucesso!")
            return processar_html_carrefour(response.text, nome_produto, url)
        else:
            print(f"❌ Erro {response.status_code}, tentando variações...")
            return tentar_urls_alternativas(nome_produto)

    except Exception as e:
        print(f"❌ Erro: {e}")
        return tentar_urls_alternativas(nome_produto)

def tentar_urls_alternativas(nome_produto):
    """
    Tenta diferentes URLs do Carrefour
    """
    # Tenta diferentes variações do termo de busca
    termos_para_testar = [
        nome_produto.replace(' ', '-'),  # coca-cola
        nome_produto.replace(' ', ''),   # cocacola
        nome_produto.split()[0],         # coca (primeira palavra)
        nome_produto                     # coca cola (original)
    ]

    urls_alternativas = []
    for termo in termos_para_testar:
        urls_alternativas.extend([
            f"https://www.carrefour.com.br/busca/{quote(termo)}",
            f"https://www.carrefour.com.br/{quote(termo)}",
        ])

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
    }

    for url in urls_alternativas:
        try:
            print(f"🔄 Tentando: {url}")
            response = requests.get(url, headers=headers, timeout=8)

            if response.status_code == 200:
                print("✅ URL alternativa funcionou!")
                return processar_html_carrefour(response.text, nome_produto, url)
            else:
                print(f"❌ {response.status_code}")

        except Exception as e:
            print(f"❌ Erro: {e}")
            continue

    return {"erro": "Não foi possível acessar o Carrefour com nenhuma URL"}

def processar_html_carrefour(html, nome_produto, url_usada):
    """
    Processa o HTML do Carrefour e extrai produtos
    """
    try:
        soup = BeautifulSoup(html, 'html.parser')
        produtos = []

        print("🔍 Procurando produtos no HTML...")

        # Busca por imagens que podem ser produtos
        todas_imagens = soup.find_all('img')
        print(f"📷 Encontradas {len(todas_imagens)} imagens")

        for img in todas_imagens:
            src = img.get('src') or img.get('data-src')
            alt = img.get('alt', '')

            # Filtra apenas imagens que parecem ser produtos
            if src and eh_imagem_produto(src, alt, nome_produto):
                # Tenta encontrar nome e preço próximos à imagem
                nome, preco = extrair_info_proxima(img, nome_produto)

                produtos.append({
                    'nome': nome,
                    'imagem': src,
                    'preco': preco
                })

                print(f"✅ Produto: {nome[:50]}...")

                if len(produtos) >= 15:  # Limita para não demorar
                    break

        if produtos:
            print(f"🎉 {len(produtos)} produtos encontrados!")
            return {
                "produtos": produtos,
                "total": len(produtos),
                "url_busca": url_usada,
                "debug_info": f"Carrefour - {len(produtos)} produtos reais encontrados"
            }
        else:
            print("😞 Nenhum produto relevante encontrado")
            return {"erro": "Nenhum produto encontrado"}

    except Exception as e:
        print(f"❌ Erro ao processar HTML: {e}")
        return {"erro": f"Erro ao processar página: {str(e)}"}

def eh_imagem_produto(src, alt, termo_busca):
    """
    Verifica se uma imagem é realmente de um produto relevante
    """
    if not src or 'data:image' in src:
        return False

    # Exclui ícones de pagamento e outros elementos não-produto
    exclusoes = [
        'mastercard', 'visa', 'elo', 'diners', 'american express',
        'boleto', 'pix', 'itau', 'bradesco', 'santander', 'nubank',
        'original', 'vale troca', 'cupom', 'carrefour', 'logo',
        'icon', 'payment', 'card'
    ]

    alt_lower = alt.lower() if alt else ''
    src_lower = src.lower()

    # Se contém palavras de exclusão, não é produto
    if any(excl in alt_lower or excl in src_lower for excl in exclusoes):
        return False

    # Verifica se a URL contém indicadores de produto do Carrefour
    indicadores_carrefour = ['carrefourbr.vtexassets.com', 'arquivos/ids']
    if any(ind in src_lower for ind in indicadores_carrefour):
        # Verifica se o alt text é relevante ao termo buscado
        if alt:
            palavras_busca = termo_busca.lower().replace(' ', '').replace('ã', 'a').replace('ç', 'c')
            # Deve conter pelo menos parte do termo buscado
            if palavras_busca in alt_lower or any(palavra in alt_lower for palavra in termo_busca.split() if len(palavra) > 2):
                return True

    return False

def extrair_info_proxima(img_element, termo_busca):
    """
    Extrai nome e preço próximos a uma imagem
    """
    nome = img_element.get('alt', 'Produto')
    preco = 'Consulte'

    # Busca no elemento pai
    parent = img_element.parent
    if parent:
        # Procura por texto que pode ser nome do produto
        textos = parent.find_all(text=True)
        for texto in textos:
            texto_limpo = texto.strip()
            if len(texto_limpo) > 10 and len(texto_limpo) < 100:
                # Se contém palavra da busca, provavelmente é o nome
                if any(palavra.lower() in texto_limpo.lower() for palavra in termo_busca.split()):
                    nome = texto_limpo
                    break

        # Procura por preço
        preco_element = parent.find(text=re.compile(r'R\$'))
        if preco_element:
            preco = preco_element.strip()

    return nome[:80], preco

# Função removida - usando apenas a versão rápida

def extrair_imagens_exemplo(nome_produto):
    """
    Função de exemplo que retorna imagens fictícias para testar o sistema
    """
    produtos_exemplo = [
        {
            "nome": f"{nome_produto.title()} Exemplo 1",
            "imagem": "https://via.placeholder.com/300x200/0066cc/ffffff?text=Produto+1",
            "preco": "R$ 19,90"
        },
        {
            "nome": f"{nome_produto.title()} Exemplo 2",
            "imagem": "https://via.placeholder.com/300x200/004499/ffffff?text=Produto+2",
            "preco": "R$ 25,50"
        },
        {
            "nome": f"{nome_produto.title()} Exemplo 3",
            "imagem": "https://via.placeholder.com/300x200/0088ff/ffffff?text=Produto+3",
            "preco": "R$ 32,00"
        }
    ]

    return {
        "produtos": produtos_exemplo,
        "total": len(produtos_exemplo),
        "url_busca": "exemplo",
        "debug_info": "Usando dados de exemplo para teste"
    }

def extrair_info_produto(container):
    """
    Extrai informações de um container de produto
    """
    try:
        # Busca pela imagem
        img = container.find('img')
        if not img:
            return None
            
        img_url = img.get('src') or img.get('data-src')
        if not img_url or not is_valid_product_image(img_url):
            return None
        
        # Busca pelo nome do produto
        nome_element = (
            container.find('h2') or 
            container.find('h3') or 
            container.find('span', class_=re.compile(r'name|title|product')) or
            container.find('a', class_=re.compile(r'name|title|product'))
        )
        nome = nome_element.get_text(strip=True) if nome_element else img.get('alt', 'Produto')
        
        # Busca pelo preço
        preco_element = container.find('span', class_=re.compile(r'price|valor|preco'))
        preco = preco_element.get_text(strip=True) if preco_element else 'N/A'
        
        return {
            'nome': nome,
            'imagem': img_url,
            'preco': preco
        }
    except:
        return None

def extrair_nome_produto_proximo(img_element):
    """
    Tenta extrair o nome do produto próximo a uma imagem
    """
    try:
        # Busca no elemento pai
        parent = img_element.parent
        if parent:
            text_elements = parent.find_all(['h1', 'h2', 'h3', 'h4', 'span', 'p', 'a'])
            for element in text_elements:
                text = element.get_text(strip=True)
                if text and len(text) > 5 and len(text) < 100:
                    return text
        return None
    except:
        return None

def is_valid_product_image(img_url):
    """
    Verifica se a URL da imagem é válida para um produto
    """
    if not img_url:
        return False
    
    # Remove URLs muito pequenas (provavelmente ícones)
    if 'data:image' in img_url:
        return False
    
    # Verifica se contém palavras-chave de produto
    product_keywords = ['product', 'item', 'catalog', 'carrefour']
    return any(keyword in img_url.lower() for keyword in product_keywords)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/favicon.ico')
def favicon():
    return '', 204

@app.route('/buscar', methods=['POST'])
def buscar_produtos():
    nome_produto = request.json.get('produto', '').strip()

    if not nome_produto:
        return jsonify({"erro": "Nome do produto é obrigatório"})

    if len(nome_produto) < 2:
        return jsonify({"erro": "Nome do produto deve ter pelo menos 2 caracteres"})

    print(f"Iniciando busca por: {nome_produto}")

    # Usa apenas a versão rápida do Carrefour
    resultado = extrair_imagens_carrefour_rapido(nome_produto)

    # Se falhar, usa exemplo apenas como último recurso
    if "erro" in resultado:
        print(f"Carrefour falhou: {resultado['erro']}")
        print("Usando dados de exemplo como último recurso...")
        resultado = extrair_imagens_exemplo(nome_produto)
        resultado["aviso"] = "⚠️ Não foi possível acessar o Carrefour. Mostrando dados de exemplo."

    return jsonify(resultado)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
