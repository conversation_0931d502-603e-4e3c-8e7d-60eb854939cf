from flask import Flask, render_template, request, jsonify
import requests
from bs4 import BeautifulSoup
import time
import re
from urllib.parse import urljoin, quote

app = Flask(__name__)

def extrair_imagens_carrefour(nome_produto):
    """
    Extrai URLs das imagens de produtos do Carrefour
    """
    try:
        # Lista de URLs para tentar
        urls_para_tentar = [
            f"https://www.carrefour.com.br/busca?q={quote(nome_produto)}",
            f"https://www.carrefour.com.br/busca/{quote(nome_produto)}",
            f"https://www.carrefour.com.br/search?q={quote(nome_produto)}",
        ]

        # Headers mais completos para simular um navegador real
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "pt-BR,pt;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0"
        }

        response = None
        url_usada = None

        # Tenta cada URL até encontrar uma que funcione
        for url in urls_para_tentar:
            try:
                print(f"Tentando URL: {url}")
                response = requests.get(url, headers=headers, timeout=15, allow_redirects=True)

                if response.status_code == 200:
                    url_usada = url
                    print(f"Sucesso com URL: {url}")
                    break
                else:
                    print(f"Erro {response.status_code} para URL: {url}")

            except requests.RequestException as e:
                print(f"Erro de conexão para URL {url}: {e}")
                continue

        if not response or response.status_code != 200:
            return {"erro": f"Não foi possível acessar o site do Carrefour. Todas as URLs falharam. Último status: {response.status_code if response else 'Sem resposta'}"}

        soup = BeautifulSoup(response.text, 'html.parser')
        
        produtos = []
        
        # Busca por diferentes padrões de produtos no HTML
        # Padrão 1: Produtos em containers VTEX
        containers_produtos = soup.find_all('div', class_=re.compile(r'vtex-product-summary.*container'))
        
        for container in containers_produtos:
            produto_info = extrair_info_produto(container)
            if produto_info:
                produtos.append(produto_info)
        
        # Padrão 2: Se não encontrou, tenta outros seletores
        if not produtos:
            # Busca por imagens com classes relacionadas a produtos
            img_tags = soup.find_all('img', class_=re.compile(r'product|item|card'))
            
            for img in img_tags:
                if img.get('src') or img.get('data-src'):
                    img_url = img.get('src') or img.get('data-src')
                    if img_url and is_valid_product_image(img_url):
                        # Tenta encontrar o nome do produto próximo à imagem
                        nome = extrair_nome_produto_proximo(img)
                        produtos.append({
                            'nome': nome or 'Produto',
                            'imagem': img_url,
                            'preco': 'N/A'
                        })
        
        # Padrão 3: Busca genérica por imagens
        if not produtos:
            all_imgs = soup.find_all('img')
            for img in all_imgs:
                img_url = img.get('src') or img.get('data-src')
                if img_url and is_valid_product_image(img_url):
                    nome = img.get('alt') or 'Produto'
                    if any(keyword in nome.lower() for keyword in nome_produto.lower().split()):
                        produtos.append({
                            'nome': nome,
                            'imagem': img_url,
                            'preco': 'N/A'
                        })
        
        return {
            "produtos": produtos[:20],  # Limita a 20 produtos
            "total": len(produtos),
            "url_busca": url_usada,
            "debug_info": f"URL usada: {url_usada}, Status: {response.status_code}, Tamanho HTML: {len(response.text)} chars"
        }
        
    except requests.RequestException as e:
        return {"erro": f"Erro de conexão: {str(e)}"}
    except Exception as e:
        return {"erro": f"Erro inesperado: {str(e)}"}

def extrair_imagens_exemplo(nome_produto):
    """
    Função de exemplo que retorna imagens fictícias para testar o sistema
    """
    produtos_exemplo = [
        {
            "nome": f"{nome_produto.title()} Exemplo 1",
            "imagem": "https://via.placeholder.com/300x200/0066cc/ffffff?text=Produto+1",
            "preco": "R$ 19,90"
        },
        {
            "nome": f"{nome_produto.title()} Exemplo 2",
            "imagem": "https://via.placeholder.com/300x200/004499/ffffff?text=Produto+2",
            "preco": "R$ 25,50"
        },
        {
            "nome": f"{nome_produto.title()} Exemplo 3",
            "imagem": "https://via.placeholder.com/300x200/0088ff/ffffff?text=Produto+3",
            "preco": "R$ 32,00"
        }
    ]

    return {
        "produtos": produtos_exemplo,
        "total": len(produtos_exemplo),
        "url_busca": "exemplo",
        "debug_info": "Usando dados de exemplo para teste"
    }

def extrair_info_produto(container):
    """
    Extrai informações de um container de produto
    """
    try:
        # Busca pela imagem
        img = container.find('img')
        if not img:
            return None
            
        img_url = img.get('src') or img.get('data-src')
        if not img_url or not is_valid_product_image(img_url):
            return None
        
        # Busca pelo nome do produto
        nome_element = (
            container.find('h2') or 
            container.find('h3') or 
            container.find('span', class_=re.compile(r'name|title|product')) or
            container.find('a', class_=re.compile(r'name|title|product'))
        )
        nome = nome_element.get_text(strip=True) if nome_element else img.get('alt', 'Produto')
        
        # Busca pelo preço
        preco_element = container.find('span', class_=re.compile(r'price|valor|preco'))
        preco = preco_element.get_text(strip=True) if preco_element else 'N/A'
        
        return {
            'nome': nome,
            'imagem': img_url,
            'preco': preco
        }
    except:
        return None

def extrair_nome_produto_proximo(img_element):
    """
    Tenta extrair o nome do produto próximo a uma imagem
    """
    try:
        # Busca no elemento pai
        parent = img_element.parent
        if parent:
            text_elements = parent.find_all(['h1', 'h2', 'h3', 'h4', 'span', 'p', 'a'])
            for element in text_elements:
                text = element.get_text(strip=True)
                if text and len(text) > 5 and len(text) < 100:
                    return text
        return None
    except:
        return None

def is_valid_product_image(img_url):
    """
    Verifica se a URL da imagem é válida para um produto
    """
    if not img_url:
        return False
    
    # Remove URLs muito pequenas (provavelmente ícones)
    if 'data:image' in img_url:
        return False
    
    # Verifica se contém palavras-chave de produto
    product_keywords = ['product', 'item', 'catalog', 'carrefour']
    return any(keyword in img_url.lower() for keyword in product_keywords)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/favicon.ico')
def favicon():
    return '', 204

@app.route('/buscar', methods=['POST'])
def buscar_produtos():
    nome_produto = request.json.get('produto', '').strip()

    if not nome_produto:
        return jsonify({"erro": "Nome do produto é obrigatório"})

    if len(nome_produto) < 2:
        return jsonify({"erro": "Nome do produto deve ter pelo menos 2 caracteres"})

    # Tenta primeiro o Carrefour
    resultado = extrair_imagens_carrefour(nome_produto)

    # Se deu erro, usa dados de exemplo para demonstração
    if "erro" in resultado:
        print(f"Erro no Carrefour: {resultado['erro']}")
        print("Usando dados de exemplo...")
        resultado = extrair_imagens_exemplo(nome_produto)
        resultado["aviso"] = "⚠️ Não foi possível acessar o Carrefour. Mostrando dados de exemplo."

    return jsonify(resultado)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
