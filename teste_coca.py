import requests
from bs4 import BeautifulSoup
from urllib.parse import quote

def teste_coca_cola():
    print("🧪 Testando 'coca cola' sem espaços...")
    
    # Remove espaços
    termo = "coca cola".replace(' ', '')  # "cocacola"
    url = f"https://www.carrefour.com.br/busca/{quote(termo)}"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    
    print(f"📡 URL: {url}")
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            imagens = soup.find_all('img')
            
            produtos_coca = 0
            for img in imagens:
                alt = img.get('alt', '').lower()
                src = img.get('src', '')
                
                if 'coca' in alt and 'carrefourbr.vtexassets.com' in src:
                    produtos_coca += 1
                    print(f"✅ {alt[:60]}...")
                    
                    if produtos_coca >= 5:  # Mostra só os primeiros 5
                        break
            
            print(f"\n🎯 Total de produtos Coca-Cola encontrados: {produtos_coca}")
            
            if produtos_coca > 0:
                print("🎉 SUCESSO! Coca-Cola encontrada!")
                return True
            else:
                print("😞 Nenhum produto Coca-Cola encontrado")
                return False
        else:
            print(f"❌ Erro {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    teste_coca_cola()
