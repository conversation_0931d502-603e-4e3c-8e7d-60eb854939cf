from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time

def testar_carrefour():
    driver = None
    try:
        print("Iniciando teste do Selenium...")
        
        # Configurações do Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        
        # Testa diferentes URLs
        urls = [
            "https://www.carrefour.com.br",
            "https://www.carrefour.com.br/busca?q=coca",
            "https://mercadolivre.com.br/coca-cola"
        ]
        
        for url in urls:
            try:
                print(f"\nTestando: {url}")
                driver.get(url)
                time.sleep(3)
                
                title = driver.title
                print(f"Título: {title}")
                
                # Verifica se há produtos
                produtos = driver.find_elements(By.CSS_SELECTOR, "img[src*='product'], img[alt*='produto'], .product")
                print(f"Elementos encontrados: {len(produtos)}")
                
                if len(produtos) > 0:
                    print("✅ Sucesso! Encontrou produtos")
                    return url
                    
            except Exception as e:
                print(f"❌ Erro: {e}")
                
        return None
        
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    resultado = testar_carrefour()
    if resultado:
        print(f"\n🎉 URL que funcionou: {resultado}")
    else:
        print("\n😞 Nenhuma URL funcionou")
